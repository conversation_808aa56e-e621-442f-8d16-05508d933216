#!/usr/bin/env python3
"""
Setup script for CBRE Data Validation AI Agent
"""

import os
import subprocess
import sys


def install_requirements():
    """Install required packages."""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements_ai_agent.txt"])
        print("✅ Requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False
    return True


def setup_openai_key():
    """Setup OpenAI API key."""
    print("\n🔑 Setting up OpenAI API Key...")
    
    # Check if already set
    if os.getenv('OPENAI_API_KEY'):
        print("✅ OPENAI_API_KEY is already set in environment")
        return True
    
    print("Please set your OpenAI API key in one of these ways:")
    print("\n1. Set environment variable (Recommended):")
    print("   Windows: set OPENAI_API_KEY=your_api_key_here")
    print("   Linux/Mac: export OPENAI_API_KEY=your_api_key_here")
    
    print("\n2. Create .env file in this directory with:")
    print("   OPENAI_API_KEY=your_api_key_here")
    
    print("\n3. Set it temporarily for this session:")
    api_key = input("Enter your OpenAI API key (or press Enter to skip): ").strip()
    
    if api_key:
        os.environ['OPENAI_API_KEY'] = api_key
        print("✅ API key set for this session")
        return True
    else:
        print("⚠️ API key not set. You'll need to set it before running the agent.")
        return False


def test_setup():
    """Test if setup is working."""
    print("\n🧪 Testing setup...")
    
    try:
        import pandas as pd
        import openai
        print("✅ All packages imported successfully")
        
        if os.getenv('OPENAI_API_KEY'):
            print("✅ OpenAI API key is available")
            return True
        else:
            print("⚠️ OpenAI API key not found")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False


def main():
    """Main setup function."""
    print("🚀 CBRE Data Validation AI Agent Setup")
    print("=" * 50)
    
    # Install requirements
    if not install_requirements():
        print("❌ Setup failed during package installation")
        return
    
    # Setup API key
    setup_openai_key()
    
    # Test setup
    if test_setup():
        print("\n🎉 Setup completed successfully!")
        print("\n📖 Usage:")
        print("   python data_validation_agent.py <excel_file_path>")
        print("\n📝 Example:")
        print("   python data_validation_agent.py data/FundHoldings_WithErrors.xlsx")
    else:
        print("\n⚠️ Setup completed with warnings. Please check the issues above.")


if __name__ == "__main__":
    main()
