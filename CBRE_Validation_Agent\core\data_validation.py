import pandas as pd
import json
from typing import Dict, Any
from core.llm_config import get_llm


class DataValidationAgent:
    """
    Data validation agent that validates uploaded file data row by row according to validation_rules_updated.json.
    Returns original data with Is_correct and Why columns.
    """

    def __init__(self, llm=None):
        self.llm = llm or get_llm()
        self.validation_rules_path = "store_procedure/validation_rules_updated.json"
        self.schema_template_path = "schema_templates/column_template.json"

    def validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Validate uploaded file data row by row according to validation_rules_updated.json.

        Args:
            df: DataFrame containing the uploaded data

        Returns:
            DataFrame with original data plus Is_correct and Why columns
        """
        result_df = df.copy()

        result_df['Is_correct'] = True
        result_df['Why'] = ''

        validation_rules = self._load_validation_rules()

        for index, row in result_df.iterrows():
            is_correct, error_message = self._validate_row(row, validation_rules)
            result_df.at[index, 'Is_correct'] = is_correct
            result_df.at[index, 'Why'] = error_message

        return result_df

    def _load_validation_rules(self) -> list:
        """Load the validation rules from validation_rules_updated.json."""
        try:
            with open(self.validation_rules_path, "r") as f:
                return json.load(f)
        except Exception as e:
            raise Exception(f"Failed to load validation rules: {e}")

    def _load_schema_template(self) -> Dict[str, Any]:
        """Load the schema template from column_template.json."""
        try:
            with open(self.schema_template_path, "r") as f:
                return json.load(f)
        except Exception as e:
            raise Exception(f"Failed to load schema template: {e}")

    def _build_expected_schema(self, schema_template: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """
        Build expected schema from template file, following stored procedure patterns.

        Args:
            schema_template: Schema template loaded from column_template.json

        Returns:
            Dict with field definitions including type, required, and nullable properties
        """
        expected_schema = {}

        for column in schema_template.get('required_columns', []):
            data_type = schema_template.get('data_types', {}).get(column, 'string')

            # Map data types from template to validation types (following CTRL_FILE_MAPPING_DETAILS pattern)
            if data_type in ['float', 'number']:
                validation_type = 'numeric'
            elif data_type == 'int':
                validation_type = 'integer'
            else:
                validation_type = 'string'

            expected_schema[column] = {
                'type': validation_type,
                'required': True,  # All columns in required_columns are mandatory
                'nullable': False  # Following stored procedure pattern
            }

        return expected_schema

    def _apply_stored_procedure_rules(self, row: pd.Series, errors: list) -> None:
        """
        Apply validation rules based on stored procedures from validation_rules_updated.json.

        Args:
            row: Single row from DataFrame
            errors: List to append errors to
        """
        # SP_CLEAN_DATA rules - Check if UNIQUE_ID starts with 'TOTAL'
        if 'UNIQUE_ID' in row and pd.notna(row['UNIQUE_ID']):
            unique_id = str(row['UNIQUE_ID']).strip().upper()
            if unique_id.startswith('TOTAL'):
                errors.append("UNIQUE_ID cannot start with 'TOTAL' (SP_CLEAN_DATA rule)")

        # SP_CLEAN_DATA rules - Check if all financial fields are zero/null
        financial_fields = ['NAV', 'OWNERSHIP_PERCENTAGE', 'CAPITAL_CALLED', 'NO_OF_SHARES', 'COMMITTED_CAPITAL']
        all_financial_zero_or_null = True

        for field in financial_fields:
            if field in row and pd.notna(row[field]):
                try:
                    if float(row[field]) != 0:
                        all_financial_zero_or_null = False
                        break
                except (ValueError, TypeError):
                    pass  # Type error already caught in schema validation

        if all_financial_zero_or_null:
            errors.append("All financial fields cannot be zero or null simultaneously (SP_CLEAN_DATA rule)")

        # SP_FINAL_VALIDATION rules - Check for null/empty UNIQUE_ID
        if 'UNIQUE_ID' in row:
            unique_id_value = row['UNIQUE_ID']
            if pd.isna(unique_id_value) or (isinstance(unique_id_value, str) and str(unique_id_value).strip() == ''):
                errors.append("UNIQUE_ID cannot be null or empty (SP_FINAL_VALIDATION rule)")

    def _validate_row(self, row: pd.Series, validation_rules: list) -> tuple:
        """
        Validate a single row according to validation rules and stored procedures.

        Args:
            row: Single row from DataFrame
            validation_rules: Validation rules from validation_rules_updated.json

        Returns:
            tuple: (is_correct: bool, error_message: str)
        """
        errors = []

        # Load expected schema from template file
        schema_template = self._load_schema_template()
        expected_schema = self._build_expected_schema(schema_template)

        # 1. Schema validation (SP_VALIDATE_MANDATORY_COLUMNS equivalent)
        for field, schema in expected_schema.items():
            if field in row:
                value = row[field]

                # Check for null/empty values
                if not schema['nullable']:
                    if pd.isna(value) or (isinstance(value, str) and str(value).strip() == ''):
                        errors.append(f"Field '{field}' cannot be null or empty (Schema validation)")
                        continue  # Skip type validation if null

                # Check data types (following CTRL_FILE_MAPPING_DETAILS pattern)
                if pd.notna(value):
                    if schema['type'] == 'numeric':
                        try:
                            float(value)
                        except (ValueError, TypeError):
                            errors.append(f"Field '{field}' must be numeric, found: '{value}' (Schema validation)")

                    elif schema['type'] == 'integer':
                        try:
                            val = float(value)
                            if val != int(val):
                                errors.append(f"Field '{field}' must be an integer, found: '{value}' (Schema validation)")
                        except (ValueError, TypeError):
                            errors.append(f"Field '{field}' must be an integer, found: '{value}' (Schema validation)")

                    elif schema['type'] == 'string':
                        # String validation - just ensure it's convertible to string
                        try:
                            str(value)
                        except:
                            errors.append(f"Field '{field}' must be a valid string (Schema validation)")
            else:
                if schema['required']:
                    errors.append(f"Required field '{field}' is missing (SP_VALIDATE_MANDATORY_COLUMNS)")

        # 2. Apply stored procedure business rules
        self._apply_stored_procedure_rules(row, errors)

        # Return validation results
        if errors:
            return False, "; ".join(errors)
        else:
            return True, ""