-- Expected file extensions
-- Accepted only: .xls or .xlsx

-- Create secure schema if not already present
CREATE SCHEMA IF NOT EXISTS SECURE_DB.SEC_MASTER;

-- Create table to receive uploaded file contents
CREATE OR REPLACE TABLE SECURE_DB.SEC_MASTER.FundHoldings (
    UNIQUE_ID STRING,
    PORTFOLIO_ID STRING,
    REGISTERED_HOLDER STRING,
    NAV NUMBER,
    OWNERSHIP_PERCENTAGE NUMBER,
    CAPITAL_CALLED NUMBER,
    NO_OF_SHARES NUMBER,
    COMMITTED_CAPITAL NUMBER,
    PERIOD STRING,
    FUND_NAME STRING
);

-- Define control table to manage expected column structure per file type
CREATE OR REPLACE TABLE SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS (
    FILE_TYPE STRING,
    COLUMN_NAME STRING,
    IS_REQUIRED BOOLEAN,
    DATA_TYPE STRING,
    POSITION INT
);

-- Example entries for FundHoldings expected structure
INSERT INTO SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS (FILE_TYPE, COLUMN_NAME, IS_REQUIRED, DATA_TYPE, POSITION)
VALUES 
('FundHoldings', 'UNIQUE_ID', TRUE, 'STRING', 1),
('FundHoldings', 'PORTFOLIO_ID', TRUE, 'STRING', 2),
('FundHoldings', 'REGISTERED_HOLDER', TRUE, 'STRING', 3),
('FundHoldings', 'NAV', TRUE, 'NUMBER', 4),
('FundHoldings', 'OWNERSHIP_PERCENTAGE', TRUE, 'NUMBER', 5),
('FundHoldings', 'CAPITAL_CALLED', TRUE, 'NUMBER', 6),
('FundHoldings', 'NO_OF_SHARES', TRUE, 'NUMBER', 7),
('FundHoldings', 'COMMITTED_CAPITAL', TRUE, 'NUMBER', 8),
('FundHoldings', 'PERIOD', TRUE, 'STRING', 9),
('FundHoldings', 'FUND_NAME', TRUE, 'STRING', 10);
