-- =============================================================================
-- ENHANCED VALIDATION RULES FOR DATA_VALIDATION.PY
-- =============================================================================
-- POINT-WISE INSTRUCTIONS FOR LLM IMPLEMENTATION:
--
-- 1. SCHEMA INTEGRATION IMPROVEMENTS:
--    - Fully utilize CTRL_FILE_MAPPING_DETAILS structure for dynamic validation
--    - Load column definitions, data types, and requirements from control table
--    - Implement position-based validation for column order verification
--    - Use IS_REQUIRED flag for mandatory field validation
--
-- 2. DAT<PERSON> TYPE VALIDATION ENHANCEMENTS:
--    - Add detailed data type validation rules for each column type
--    - Implement numeric range validation for financial fields
--    - Add string length and format validation
--    - Include data precision validation for decimal numbers
--
-- 3. ROW-LEVEL VALIDATION IMPLEMENTATION:
--    - Convert table-level stored procedures to row-level validation logic
--    - Implement individual row validation with specific error messages
--    - Add business rule validation for each data row
--    - Provide detailed error reporting per field per row
-- =============================================================================

-- Expected file extensions
-- Accepted only: .xls or .xlsx
-- LLM INSTRUCTION: Validate file extension before processing any data

-- Create secure schema if not already present
CREATE SCHEMA IF NOT EXISTS SECURE_DB.SEC_MASTER;

-- Create table to receive uploaded file contents
CREATE OR REPLACE TABLE SECURE_DB.SEC_MASTER.FundHoldings (
    UNIQUE_ID STRING,
    PORTFOLIO_ID STRING,
    REGISTERED_HOLDER STRING,
    NAV NUMBER,
    OWNERSHIP_PERCENTAGE NUMBER,
    CAPITAL_CALLED NUMBER,
    NO_OF_SHARES NUMBER,
    COMMITTED_CAPITAL NUMBER,
    PERIOD STRING,
    FUND_NAME STRING
);

-- ENHANCED CONTROL TABLE with additional validation metadata
CREATE OR REPLACE TABLE SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS (
    FILE_TYPE STRING,
    COLUMN_NAME STRING,
    IS_REQUIRED BOOLEAN,
    DATA_TYPE STRING,
    POSITION INT,
    -- NEW FIELDS FOR ENHANCED VALIDATION:
    MIN_LENGTH INT,           -- Minimum string length
    MAX_LENGTH INT,           -- Maximum string length
    MIN_VALUE NUMBER,         -- Minimum numeric value
    MAX_VALUE NUMBER,         -- Maximum numeric value
    DECIMAL_PLACES INT,       -- Number of decimal places for numbers
    REGEX_PATTERN STRING,     -- Regular expression for format validation
    NULLABLE BOOLEAN,         -- Whether field can be null
    DEFAULT_VALUE STRING,     -- Default value if empty
    VALIDATION_RULES STRING   -- JSON string with additional validation rules
);

-- ENHANCED ENTRIES for FundHoldings with detailed validation rules
-- LLM INSTRUCTION: Use these entries to build comprehensive row-level validation
INSERT INTO SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS
(FILE_TYPE, COLUMN_NAME, IS_REQUIRED, DATA_TYPE, POSITION, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE, DECIMAL_PLACES, REGEX_PATTERN, NULLABLE, DEFAULT_VALUE, VALIDATION_RULES)
VALUES
('FundHoldings', 'UNIQUE_ID', TRUE, 'STRING', 1, 1, 50, NULL, NULL, NULL, '^[A-Za-z0-9]+$', FALSE, NULL,
 '{"rules": ["not_starts_with_TOTAL", "no_special_chars", "unique_across_dataset"]}'),

('FundHoldings', 'PORTFOLIO_ID', TRUE, 'STRING', 2, 1, 30, NULL, NULL, NULL, '^[A-Za-z0-9_-]+$', FALSE, NULL,
 '{"rules": ["alphanumeric_with_underscore_dash"]}'),

('FundHoldings', 'REGISTERED_HOLDER', TRUE, 'STRING', 3, 1, 100, NULL, NULL, NULL, NULL, FALSE, NULL,
 '{"rules": ["not_empty", "valid_string"]}'),

('FundHoldings', 'NAV', TRUE, 'NUMBER', 4, NULL, NULL, 0, 999999999999.99, 2, NULL, FALSE, '0',
 '{"rules": ["positive_or_zero", "max_2_decimals", "not_all_financial_zero"]}'),

('FundHoldings', 'OWNERSHIP_PERCENTAGE', TRUE, 'NUMBER', 5, NULL, NULL, 0, 100, 4, NULL, FALSE, '0',
 '{"rules": ["percentage_range", "max_4_decimals", "not_all_financial_zero"]}'),

('FundHoldings', 'CAPITAL_CALLED', TRUE, 'NUMBER', 6, NULL, NULL, 0, 999999999999.99, 2, NULL, FALSE, '0',
 '{"rules": ["positive_or_zero", "max_2_decimals", "not_all_financial_zero"]}'),

('FundHoldings', 'NO_OF_SHARES', TRUE, 'NUMBER', 7, NULL, NULL, 0, 999999999, 0, NULL, FALSE, '0',
 '{"rules": ["integer_only", "positive_or_zero", "not_all_financial_zero"]}'),

('FundHoldings', 'COMMITTED_CAPITAL', TRUE, 'NUMBER', 8, NULL, NULL, 0, 999999999999.99, 2, NULL, FALSE, '0',
 '{"rules": ["positive_or_zero", "max_2_decimals", "not_all_financial_zero"]}'),

('FundHoldings', 'PERIOD', TRUE, 'STRING', 9, 1, 20, NULL, NULL, NULL, '^Q[1-4]-[0-9]{4}$|^[0-9]{4}-Q[1-4]$|^[0-9]{4}$', FALSE, NULL,
 '{"rules": ["period_format", "valid_quarter_year"]}'),

('FundHoldings', 'FUND_NAME', TRUE, 'STRING', 10, 1, 100, NULL, NULL, NULL, NULL, FALSE, NULL,
 '{"rules": ["not_empty", "valid_fund_name"]}');

-- =============================================================================
-- ENHANCED STORED PROCEDURES WITH ROW-LEVEL VALIDATION INSTRUCTIONS
-- =============================================================================

-- LLM INSTRUCTION 1: FILE STRUCTURE VALIDATION
-- Convert this table-level procedure to row-level validation in data_validation.py
-- Check each row has the correct number of columns and column names match expected schema
CREATE OR REPLACE PROCEDURE SECURE_DB.SEC_MASTER.SP_VALIDATE_FILE_STRUCTURE(FILE_TYPE STRING)
RETURNS STRING
LANGUAGE SQL
AS
$$
DECLARE
    COL_COUNT_TEMPLATE INT;
    COL_COUNT_UPLOAD INT;
BEGIN
    -- LLM IMPLEMENTATION INSTRUCTION:
    -- 1. Load expected column count from CTRL_FILE_MAPPING_DETAILS
    -- 2. Validate each row has exactly this many non-null columns
    -- 3. Validate column names match expected names in correct positions
    -- 4. Return specific error for each row that fails validation

    SELECT COUNT(*) INTO COL_COUNT_TEMPLATE
    FROM SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS
    WHERE FILE_TYPE = FILE_TYPE;

    SELECT COUNT(*) INTO COL_COUNT_UPLOAD
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'SEC_MASTER'
      AND TABLE_NAME = 'FundHoldings';

    IF COL_COUNT_TEMPLATE != COL_COUNT_UPLOAD THEN
        RETURN 'File structure mismatch. Expected ' || COL_COUNT_TEMPLATE || ' columns, found ' || COL_COUNT_UPLOAD;
    END IF;

    RETURN 'File structure valid';
END;
$$;

-- LLM INSTRUCTION 2: MANDATORY COLUMNS VALIDATION
-- Convert this to row-level validation checking each required field per row
CREATE OR REPLACE PROCEDURE SECURE_DB.SEC_MASTER.SP_VALIDATE_MANDATORY_COLUMNS(FILE_TYPE STRING)
RETURNS STRING
LANGUAGE SQL
AS
$$
DECLARE
    MISSING_COUNT INT;
BEGIN
    -- LLM IMPLEMENTATION INSTRUCTION:
    -- 1. For each row, check all fields marked as IS_REQUIRED=TRUE in CTRL_FILE_MAPPING_DETAILS
    -- 2. Validate each required field is not null, not empty, and not whitespace-only
    -- 3. Use NULLABLE flag to determine if null values are allowed
    -- 4. Return specific error message listing which required fields are missing per row

    SELECT COUNT(*) INTO MISSING_COUNT
    FROM SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS c
    LEFT JOIN INFORMATION_SCHEMA.COLUMNS u
    ON c.COLUMN_NAME = u.COLUMN_NAME
       AND u.TABLE_SCHEMA = 'SEC_MASTER'
       AND u.TABLE_NAME = 'FundHoldings'
    WHERE c.FILE_TYPE = FILE_TYPE
      AND c.IS_REQUIRED = TRUE
      AND u.COLUMN_NAME IS NULL;

    IF MISSING_COUNT > 0 THEN
        RETURN 'Missing required columns.';
    END IF;

    RETURN 'All required columns present.';
END;
$$;

-- LLM INSTRUCTION 3: DATA TYPE VALIDATION (NEW ENHANCED PROCEDURE)
-- Implement this as row-level validation in data_validation.py
CREATE OR REPLACE PROCEDURE SECURE_DB.SEC_MASTER.SP_VALIDATE_DATA_TYPES()
RETURNS STRING
LANGUAGE SQL
AS
$$
BEGIN
    -- LLM IMPLEMENTATION INSTRUCTION:
    -- 1. For each row and each column, validate data type matches DATA_TYPE in CTRL_FILE_MAPPING_DETAILS
    -- 2. For STRING fields: Check MIN_LENGTH, MAX_LENGTH, REGEX_PATTERN if specified
    -- 3. For NUMBER fields: Check MIN_VALUE, MAX_VALUE, DECIMAL_PLACES constraints
    -- 4. Apply VALIDATION_RULES JSON for additional business logic per field
    -- 5. Return specific error message for each field that fails validation

    -- Example validation logic to implement in Python:
    -- IF DATA_TYPE = 'STRING' THEN
    --     - Check length constraints
    --     - Validate regex pattern if provided
    --     - Apply string-specific validation rules
    -- IF DATA_TYPE = 'NUMBER' THEN
    --     - Validate numeric format
    --     - Check range constraints
    --     - Validate decimal places
    --     - Apply numeric-specific validation rules

    RETURN 'Data type validation completed';
END;
$$;

-- LLM INSTRUCTION 4: ENHANCED DATA CLEANING WITH ROW-LEVEL LOGIC
-- Convert this table-level procedure to row-level validation rules
CREATE OR REPLACE PROCEDURE SECURE_DB.SEC_MASTER.SP_CLEAN_DATA()
RETURNS STRING
LANGUAGE SQL
AS
$$
BEGIN
    -- LLM IMPLEMENTATION INSTRUCTION:
    -- 1. For each row, check if all financial fields (NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL) are zero or null
    -- 2. Mark row as invalid if all financial fields are zero/null with error: "All financial fields cannot be zero or null simultaneously"
    -- 3. Check if UNIQUE_ID starts with 'TOTAL' (case-insensitive)
    -- 4. Mark row as invalid if UNIQUE_ID starts with 'TOTAL' with error: "UNIQUE_ID cannot start with 'TOTAL'"
    -- 5. Apply regex cleaning to UNIQUE_ID: remove all non-alphanumeric characters
    -- 6. Validate cleaned UNIQUE_ID still meets length and format requirements

    -- Original table-level logic (convert to row-level):
    DELETE FROM SECURE_DB.SEC_MASTER.FundHoldings
    WHERE (NVL(NAV,0) = 0)
      AND (NVL(OWNERSHIP_PERCENTAGE,0) = 0)
      AND (NVL(CAPITAL_CALLED,0) = 0)
      AND (NVL(NO_OF_SHARES,0) = 0)
      AND (NVL(COMMITTED_CAPITAL,0) = 0);

    DELETE FROM SECURE_DB.SEC_MASTER.FundHoldings
    WHERE UPPER(UNIQUE_ID) LIKE 'TOTAL%';

    UPDATE SECURE_DB.SEC_MASTER.FundHoldings
    SET UNIQUE_ID = REGEXP_REPLACE(UNIQUE_ID, '[^A-Za-z0-9]', '', 'g');

    RETURN 'Data cleaned successfully.';
END;
$$;

-- LLM INSTRUCTION 5: ENHANCED FINAL VALIDATION WITH ROW-LEVEL CHECKS
-- Convert this table-level procedure to comprehensive row-level validation
CREATE OR REPLACE PROCEDURE SECURE_DB.SEC_MASTER.SP_FINAL_VALIDATION()
RETURNS STRING
LANGUAGE SQL
AS
$$
DECLARE
    REC_COUNT INT;
    DUP_COUNT INT;
    NULL_UID_COUNT INT;
BEGIN
    -- LLM IMPLEMENTATION INSTRUCTION:
    -- 1. Check if dataset is empty (no valid rows after cleaning)
    -- 2. For each row, validate UNIQUE_ID is not null and not empty string
    -- 3. Check for duplicate UNIQUE_IDs across the entire dataset
    -- 4. Validate business rules from VALIDATION_RULES JSON for each applicable field
    -- 5. Apply cross-field validation rules (e.g., percentage fields sum constraints)
    -- 6. Return detailed validation report with row-specific errors

    -- Original table-level logic (convert to row-level validation):
    SELECT COUNT(*) INTO REC_COUNT
    FROM SECURE_DB.SEC_MASTER.FundHoldings;
    IF REC_COUNT = 0 THEN
        RETURN 'Validation failed: File is empty.';
    END IF;

    SELECT COUNT(*) INTO NULL_UID_COUNT
    FROM SECURE_DB.SEC_MASTER.FundHoldings
    WHERE UNIQUE_ID IS NULL OR TRIM(UNIQUE_ID) = '';

    IF NULL_UID_COUNT > 0 THEN
        RETURN 'Validation failed: Unique Identifier missing.';
    END IF;

    SELECT COUNT(*) INTO DUP_COUNT
    FROM (
        SELECT UNIQUE_ID, COUNT(*)
        FROM SECURE_DB.SEC_MASTER.FundHoldings
        GROUP BY UNIQUE_ID
        HAVING COUNT(*) > 1
    );

    IF DUP_COUNT > 0 THEN
        RETURN 'Validation failed: Duplicate Unique Identifiers.';
    END IF;

    RETURN 'Final validation passed.';
END;
$$;

-- =============================================================================
-- COMPREHENSIVE LLM IMPLEMENTATION INSTRUCTIONS FOR DATA_VALIDATION.PY
-- =============================================================================

/*
POINT-BY-POINT IMPLEMENTATION GUIDE FOR LLM:

1. SCHEMA INTEGRATION IMPROVEMENTS:
   a) Load CTRL_FILE_MAPPING_DETAILS data into Python dictionary/object
   b) Use FILE_TYPE, COLUMN_NAME, IS_REQUIRED, DATA_TYPE, POSITION for validation
   c) Implement dynamic schema loading instead of hardcoded field definitions
   d) Use NULLABLE, MIN_LENGTH, MAX_LENGTH, MIN_VALUE, MAX_VALUE for constraints
   e) Parse VALIDATION_RULES JSON for additional business logic per field

2. DATA TYPE VALIDATION ENHANCEMENTS:
   a) For STRING fields:
      - Validate length: len(value) >= MIN_LENGTH and len(value) <= MAX_LENGTH
      - Apply regex validation: re.match(REGEX_PATTERN, value) if REGEX_PATTERN exists
      - Check for empty/whitespace if not NULLABLE
   b) For NUMBER fields:
      - Validate numeric format: can convert to float/int
      - Check range: MIN_VALUE <= value <= MAX_VALUE
      - Validate decimal places: round(value, DECIMAL_PLACES) == value
      - For integers: ensure no decimal component

3. ROW-LEVEL VALIDATION IMPLEMENTATION:
   a) Create validate_row() method that processes each row individually
   b) For each field in row:
      - Get field definition from CTRL_FILE_MAPPING_DETAILS
      - Apply data type validation
      - Apply constraint validation (length, range, format)
      - Apply business rules from VALIDATION_RULES JSON
   c) Return tuple: (is_valid: bool, error_messages: list)
   d) Aggregate all field errors for comprehensive row validation result

4. BUSINESS RULES IMPLEMENTATION:
   a) Financial fields validation:
      - Check if all financial fields (NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL) are zero/null
      - Error: "All financial fields cannot be zero or null simultaneously"
   b) UNIQUE_ID validation:
      - Check if starts with 'TOTAL': error "UNIQUE_ID cannot start with 'TOTAL'"
      - Apply regex cleaning: re.sub(r'[^A-Za-z0-9]', '', unique_id)
      - Validate uniqueness across dataset
   c) Period format validation:
      - Match regex pattern for quarter/year format
      - Validate logical date ranges

5. ERROR REPORTING ENHANCEMENTS:
   a) Provide field-specific error messages
   b) Include validation rule source (e.g., "Schema validation", "SP_CLEAN_DATA rule")
   c) Return original data with additional columns: Is_correct, Why
   d) Aggregate multiple errors per row with semicolon separation

6. INTEGRATION WITH EXISTING FILES:
   a) Use column_template.json as fallback if CTRL_FILE_MAPPING_DETAILS not available
   b) Maintain compatibility with validation_rules_updated.json
   c) Load enhanced_validation_rules.json for comprehensive rule definitions
   d) Implement graceful degradation if enhanced features not available

EXAMPLE IMPLEMENTATION STRUCTURE:

class DataValidationAgent:
    def __init__(self):
        self.schema_rules = self._load_schema_rules()
        self.business_rules = self._load_business_rules()

    def _load_schema_rules(self):
        # Load from CTRL_FILE_MAPPING_DETAILS or column_template.json
        pass

    def validate_data(self, df):
        # Apply file-level validation first
        # Then validate each row individually
        # Return enhanced results with detailed error reporting
        pass

    def _validate_row(self, row, row_index):
        # Implement comprehensive row-level validation
        # Apply schema validation, data type validation, business rules
        # Return detailed validation results
        pass
*/
