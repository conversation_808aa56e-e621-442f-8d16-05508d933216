{"file_type": "FundHoldings", "schema_definition": {"table_name": "SECURE_DB.SEC_MASTER.FundHoldings", "columns": [{"column_name": "UNIQUE_ID", "data_type": "STRING", "is_required": true, "nullable": false, "position": 1, "validation_rules": [{"rule_type": "not_null", "error_message": "UNIQUE_ID cannot be null or empty (SP_FINAL_VALIDATION rule)"}, {"rule_type": "not_starts_with", "value": "TOTAL", "error_message": "UNIQUE_ID cannot start with 'TOTAL' (SP_CLEAN_DATA rule)"}, {"rule_type": "regex_clean", "pattern": "[^A-Za-z0-9]", "replacement": "", "description": "Remove special characters (SP_CLEAN_DATA rule)"}]}, {"column_name": "PORTFOLIO_ID", "data_type": "STRING", "is_required": true, "nullable": false, "position": 2, "validation_rules": [{"rule_type": "not_null", "error_message": "PORTFOLIO_ID cannot be null or empty"}]}, {"column_name": "REGISTERED_HOLDER", "data_type": "STRING", "is_required": true, "nullable": false, "position": 3, "validation_rules": [{"rule_type": "not_null", "error_message": "REGISTERED_HOLDER cannot be null or empty"}]}, {"column_name": "NAV", "data_type": "NUMBER", "is_required": true, "nullable": false, "position": 4, "validation_rules": [{"rule_type": "not_null", "error_message": "NAV cannot be null or empty"}, {"rule_type": "numeric", "error_message": "NAV must be a valid number"}]}, {"column_name": "OWNERSHIP_PERCENTAGE", "data_type": "NUMBER", "is_required": true, "nullable": false, "position": 5, "validation_rules": [{"rule_type": "not_null", "error_message": "OWNERSHIP_PERCENTAGE cannot be null or empty"}, {"rule_type": "numeric", "error_message": "OWNERSHIP_PERCENTAGE must be a valid number"}]}, {"column_name": "CAPITAL_CALLED", "data_type": "NUMBER", "is_required": true, "nullable": false, "position": 6, "validation_rules": [{"rule_type": "not_null", "error_message": "CAPITAL_CALLED cannot be null or empty"}, {"rule_type": "numeric", "error_message": "CAPITAL_CALLED must be a valid number"}]}, {"column_name": "NO_OF_SHARES", "data_type": "NUMBER", "is_required": true, "nullable": false, "position": 7, "validation_rules": [{"rule_type": "not_null", "error_message": "NO_OF_SHARES cannot be null or empty"}, {"rule_type": "integer", "error_message": "NO_OF_SHARES must be a valid integer"}]}, {"column_name": "COMMITTED_CAPITAL", "data_type": "NUMBER", "is_required": true, "nullable": false, "position": 8, "validation_rules": [{"rule_type": "not_null", "error_message": "COMMITTED_CAPITAL cannot be null or empty"}, {"rule_type": "numeric", "error_message": "COMMITTED_CAPITAL must be a valid number"}]}, {"column_name": "PERIOD", "data_type": "STRING", "is_required": true, "nullable": false, "position": 9, "validation_rules": [{"rule_type": "not_null", "error_message": "PERIOD cannot be null or empty"}]}, {"column_name": "FUND_NAME", "data_type": "STRING", "is_required": true, "nullable": false, "position": 10, "validation_rules": [{"rule_type": "not_null", "error_message": "FUND_NAME cannot be null or empty"}]}]}, "business_rules": [{"rule_name": "financial_fields_not_all_zero", "description": "All financial fields cannot be zero or null simultaneously (SP_CLEAN_DATA rule)", "fields": ["NAV", "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES", "COMMITTED_CAPITAL"], "rule_type": "not_all_zero_or_null", "error_message": "All financial fields cannot be zero or null simultaneously (SP_CLEAN_DATA rule)"}, {"rule_name": "unique_id_validation", "description": "UNIQUE_ID must be unique across all records (SP_FINAL_VALIDATION rule)", "fields": ["UNIQUE_ID"], "rule_type": "unique", "error_message": "Duplicate UNIQUE_ID found (SP_FINAL_VALIDATION rule)"}], "stored_procedures": [{"procedure_name": "SP_VALIDATE_FILE_STRUCTURE", "description": "Validate file structure for FundHoldings", "validation_type": "file_level"}, {"procedure_name": "SP_VALIDATE_MANDATORY_COLUMNS", "description": "Validate mandatory columns for FundHoldings", "validation_type": "file_level"}, {"procedure_name": "SP_CLEAN_DATA", "description": "Clean FundHoldings data", "validation_type": "row_level"}, {"procedure_name": "SP_FINAL_VALIDATION", "description": "Final validation for FundHoldings data", "validation_type": "dataset_level"}]}