# CBRE Data Validation AI Agent

🤖 **Standalone AI-powered data validation agent for CBRE FundHoldings Excel files using OpenAI API**

## 🎯 Overview

This AI agent validates Excel files containing FundHoldings data using OpenAI's GPT-4 model and comprehensive validation rules. It provides intelligent, context-aware validation with detailed error reporting.

## ✨ Features

- **🤖 AI-Powered Validation**: Uses OpenAI GPT-4 for intelligent data validation
- **📋 Comprehensive Rules**: Loads validation rules from JSON configuration files
- **🔍 Row-Level Analysis**: Validates each row individually with specific error messages
- **🔄 Duplicate Detection**: Identifies duplicate UNIQUE_IDs across the dataset
- **📊 Business Logic**: Implements CBRE-specific business rules and stored procedures
- **💾 Excel Output**: Saves results with validation status and error details
- **🛡️ Fallback Validation**: Basic validation if AI API is unavailable

## 🚀 Quick Start

### 1. Setup
```bash
# Run the setup script
python setup_ai_agent.py

# Or install manually
pip install -r requirements_ai_agent.txt
```

### 2. Set OpenAI API Key
```bash
# Windows
set OPENAI_API_KEY=your_api_key_here

# Linux/Mac
export OPENAI_API_KEY=your_api_key_here
```

### 3. Run Validation
```bash
python data_validation_agent.py data/FundHoldings_WithErrors.xlsx
```

## 📁 File Structure

```
CBRE_Validation_Agent/
├── data_validation_agent.py          # Main AI agent file
├── setup_ai_agent.py                 # Setup script
├── requirements_ai_agent.txt         # Python dependencies
├── store_procedure/
│   ├── enhanced_validation_rules.json    # Detailed validation rules
│   └── validation_rules_updated.json     # Stored procedure definitions
└── data/
    ├── FundHoldings_WithErrors.xlsx      # Sample test file
    └── FundHoldings_Data_Correct.xlsx    # Sample correct file
```

## 🔧 Configuration Files

### enhanced_validation_rules.json
Contains detailed column definitions with:
- Data types and constraints
- Validation rules per field
- Business logic specifications
- Error messages

### validation_rules_updated.json
Contains stored procedure definitions:
- SP_VALIDATE_FILE_STRUCTURE
- SP_VALIDATE_MANDATORY_COLUMNS
- SP_CLEAN_DATA
- SP_FINAL_VALIDATION

## 📊 Validation Rules

### Schema Validation
- **Data Types**: STRING, NUMBER validation
- **Required Fields**: Null/empty checks
- **Constraints**: Length limits, numeric ranges

### Business Rules
- **UNIQUE_ID**: Cannot start with 'TOTAL', must be unique
- **Financial Fields**: Cannot all be zero/null simultaneously
- **Format Validation**: Regex patterns for specific fields

### Stored Procedure Logic
- **File Structure**: Column count and name validation
- **Mandatory Columns**: Required field presence
- **Data Cleaning**: Remove invalid records
- **Final Validation**: Duplicate and consistency checks

## 📈 Output

The agent generates an Excel file with:
- **Original Data**: All original columns preserved
- **Is_correct**: Boolean validation status per row
- **Why**: Detailed error messages for failed validations

### Sample Output
```
Row 1: ✅ Valid
Row 2: ❌ UNIQUE_ID cannot start with 'TOTAL'
Row 3: ❌ All financial fields cannot be zero or null simultaneously
Row 4: ❌ Duplicate UNIQUE_ID 'UID_8637' found
```

## 🛠️ Usage Examples

### Basic Validation
```bash
python data_validation_agent.py data/FundHoldings_WithErrors.xlsx
```

### With Custom File
```bash
python data_validation_agent.py /path/to/your/file.xlsx
```

## 🔍 AI Validation Process

1. **Load Configuration**: Reads validation rules from JSON files
2. **Prepare Data**: Converts Excel data to JSON format
3. **Create Prompt**: Builds comprehensive validation prompt
4. **AI Analysis**: Sends data to OpenAI GPT-4 for validation
5. **Process Results**: Parses AI response and applies to dataset
6. **Generate Output**: Creates validated Excel file with results

## 🛡️ Error Handling

- **API Failures**: Falls back to basic validation rules
- **File Errors**: Clear error messages for file issues
- **Configuration Errors**: Graceful handling of missing config files
- **Data Errors**: Detailed validation error reporting

## 📋 Requirements

- Python 3.7+
- OpenAI API key
- Internet connection for AI validation
- Excel files (.xlsx, .xls)

## 🔑 API Key Setup

### Option 1: Environment Variable (Recommended)
```bash
export OPENAI_API_KEY=your_api_key_here
```

### Option 2: .env File
Create `.env` file:
```
OPENAI_API_KEY=your_api_key_here
```

### Option 3: Runtime Input
The agent will prompt for API key if not found.

## 🎯 Benefits

- **🤖 Intelligent**: AI understands context and business logic
- **🔧 Configurable**: Rules defined in external JSON files
- **📊 Comprehensive**: Validates data types, business rules, and relationships
- **💾 Persistent**: Saves detailed validation results
- **🛡️ Reliable**: Fallback validation ensures operation continuity
- **📈 Scalable**: Can handle large datasets efficiently

## 🚨 Important Notes

- Requires valid OpenAI API key
- First 20 rows sent to AI for analysis (cost optimization)
- Fallback validation covers entire dataset
- Results saved with timestamp to avoid overwrites
- Supports both .xlsx and .xls file formats

## 📞 Support

For issues or questions:
1. Check validation rules in JSON configuration files
2. Verify OpenAI API key is set correctly
3. Review error messages in console output
4. Check generated Excel file for detailed validation results
