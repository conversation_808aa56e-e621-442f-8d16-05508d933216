#!/usr/bin/env python3
"""
CBRE Data Validation AI Agent
Standalone AI agent that validates Excel files using OpenAI API and validation rules.
"""

import pandas as pd
import json
import os
import sys
from typing import Dict, Any, List
import openai
from datetime import datetime


class DataValidationAIAgent:
    def __init__(self):
        # Load environment variables from .env file
        self._load_env_file()

        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY not found. Please set it in environment variables or .env file")
        
        openai.api_key = self.openai_api_key
        
        # Load validation rules
        self.enhanced_rules = self._load_enhanced_rules()
        self.stored_procedures = self._load_stored_procedures()
        
        print("✅ CBRE Data Validation AI Agent initialized")
        print(f"📋 Loaded {len(self.enhanced_rules.get('schema_definition', {}).get('columns', []))} column definitions")
        print(f"🔧 Loaded {len(self.stored_procedures)} stored procedures")

    def _load_env_file(self):
        """Load environment variables from .env file if it exists."""
        env_file_path = ".env"
        if os.path.exists(env_file_path):
            try:
                with open(env_file_path, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            # Remove quotes if present
                            value = value.strip().strip('"').strip("'")
                            os.environ[key.strip()] = value
                print("✅ Loaded environment variables from .env file")
            except Exception as e:
                print(f"⚠️ Warning: Could not load .env file: {e}")

    def _load_enhanced_rules(self) -> Dict[str, Any]:
        """Load enhanced validation rules from JSON file."""
        try:
            with open("store_procedure/enhanced_validation_rules.json", "r") as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ Warning: Could not load enhanced rules: {e}")
            return {}

    def _load_stored_procedures(self) -> List[Dict[str, Any]]:
        """Load stored procedure definitions from JSON file."""
        try:
            with open("store_procedure/validation_rules_updated.json", "r") as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ Warning: Could not load stored procedures: {e}")
            return []

    def _create_validation_prompt(self, data_sample: str) -> str:
        """Create comprehensive validation prompt for OpenAI."""
        
        # Extract column definitions
        columns = self.enhanced_rules.get("schema_definition", {}).get("columns", [])
        business_rules = self.enhanced_rules.get("business_rules", [])
        
        # Create detailed schema description
        schema_description = "COLUMN DEFINITIONS:\n"
        for col in columns:
            schema_description += f"- {col['column_name']}: {col['data_type']}, Required: {col['is_required']}, Nullable: {col['nullable']}\n"
            for rule in col.get('validation_rules', []):
                schema_description += f"  * {rule['rule_type']}: {rule.get('error_message', 'Validation rule')}\n"
        
        # Create business rules description
        business_description = "\nBUSINESS RULES:\n"
        for rule in business_rules:
            business_description += f"- {rule['rule_name']}: {rule['description']}\n"
        
        # Create stored procedure context
        sp_description = "\nSTORED PROCEDURE LOGIC:\n"
        for sp in self.stored_procedures:
            sp_description += f"- {sp['procedure_name']}: {sp['description']}\n"
        
        prompt = f"""
You are a CBRE Data Validation Expert. Validate the following FundHoldings data according to these rules:

{schema_description}
{business_description}
{sp_description}

VALIDATION REQUIREMENTS:
1. Check each row for data type compliance
2. Validate required fields are not null/empty
3. Apply business rules (UNIQUE_ID cannot start with 'TOTAL', financial fields cannot all be zero)
4. Check for duplicate UNIQUE_IDs across the dataset
5. Validate numeric ranges and string formats

DATA TO VALIDATE:
{data_sample}

RESPONSE FORMAT:
Return a JSON object with this structure:
{{
    "validation_results": [
        {{
            "row_index": 0,
            "is_correct": true/false,
            "errors": ["list of specific error messages"]
        }}
    ],
    "summary": {{
        "total_rows": number,
        "correct_rows": number,
        "incorrect_rows": number,
        "duplicate_unique_ids": ["list of duplicate IDs found"]
    }}
}}

Be thorough and specific in error messages. Include the validation rule source (e.g., "SP_CLEAN_DATA rule", "Schema validation").
"""
        return prompt

    def validate_file(self, file_path: str) -> pd.DataFrame:
        """Validate Excel file using OpenAI API."""
        print(f"\n🔍 Starting validation of: {file_path}")
        
        # Load Excel file
        try:
            if file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path, engine='openpyxl')
            elif file_path.endswith('.xls'):
                df = pd.read_excel(file_path, engine='xlrd')
            else:
                raise ValueError("Unsupported file format. Only .xls and .xlsx files are supported.")
            
            print(f"📊 File loaded: {len(df)} rows, {len(df.columns)} columns")
            print(f"📋 Columns: {list(df.columns)}")
            
        except Exception as e:
            print(f"❌ Error loading file: {e}")
            return pd.DataFrame()

        # Prepare data for AI validation
        data_sample = df.head(20).to_json(orient='records', indent=2)  # Send first 20 rows for analysis
        
        # Create validation prompt
        prompt = self._create_validation_prompt(data_sample)
        
        print(f"🤖 Sending data to OpenAI for validation...")
        
        try:
            # Call OpenAI API
            response = openai.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a data validation expert specializing in financial data validation for CBRE FundHoldings."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4000,
                temperature=0.1
            )
            
            # Parse AI response
            ai_response = response.choices[0].message.content
            print(f"✅ Received validation results from OpenAI")
            
            # Process AI validation results
            result_df = self._process_ai_results(df, ai_response)
            
            return result_df
            
        except Exception as e:
            print(f"❌ Error calling OpenAI API: {e}")
            # Fallback to basic validation
            return self._fallback_validation(df)

    def _process_ai_results(self, df: pd.DataFrame, ai_response: str) -> pd.DataFrame:
        """Process AI validation results and create output DataFrame."""
        result_df = df.copy()
        result_df['Is_correct'] = True
        result_df['Why'] = ''
        
        try:
            # Extract JSON from AI response
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}') + 1
            json_str = ai_response[start_idx:end_idx]
            
            validation_data = json.loads(json_str)
            
            # Apply validation results
            for result in validation_data.get('validation_results', []):
                row_idx = result.get('row_index', 0)
                if row_idx < len(result_df):
                    result_df.at[row_idx, 'Is_correct'] = result.get('is_correct', True)
                    result_df.at[row_idx, 'Why'] = '; '.join(result.get('errors', []))
            
            # Print summary
            summary = validation_data.get('summary', {})
            print(f"\n📊 Validation Summary:")
            print(f"   Total rows: {summary.get('total_rows', len(df))}")
            print(f"   Correct rows: {summary.get('correct_rows', 0)}")
            print(f"   Incorrect rows: {summary.get('incorrect_rows', 0)}")
            
            duplicates = summary.get('duplicate_unique_ids', [])
            if duplicates:
                print(f"   Duplicate UNIQUE_IDs: {duplicates}")
            
        except Exception as e:
            print(f"⚠️ Error processing AI results: {e}")
            print(f"Raw AI response: {ai_response}")
            
        return result_df

    def _fallback_validation(self, df: pd.DataFrame) -> pd.DataFrame:
        """Fallback validation if AI API fails."""
        print("🔄 Using fallback validation...")
        result_df = df.copy()
        result_df['Is_correct'] = True
        result_df['Why'] = ''
        
        # Basic validation rules
        for index, row in result_df.iterrows():
            errors = []
            
            # Check UNIQUE_ID
            if 'UNIQUE_ID' in row:
                if pd.isna(row['UNIQUE_ID']) or str(row['UNIQUE_ID']).strip() == '':
                    errors.append("UNIQUE_ID cannot be null or empty")
                elif str(row['UNIQUE_ID']).upper().startswith('TOTAL'):
                    errors.append("UNIQUE_ID cannot start with 'TOTAL'")
            
            # Check financial fields
            financial_fields = ['NAV', 'OWNERSHIP_PERCENTAGE', 'CAPITAL_CALLED', 'NO_OF_SHARES', 'COMMITTED_CAPITAL']
            all_zero = all(pd.isna(row.get(field, 0)) or row.get(field, 0) == 0 for field in financial_fields if field in row)
            if all_zero:
                errors.append("All financial fields cannot be zero or null simultaneously")
            
            if errors:
                result_df.at[index, 'Is_correct'] = False
                result_df.at[index, 'Why'] = '; '.join(errors)
        
        return result_df

    def save_results(self, result_df: pd.DataFrame, original_file_path: str) -> str:
        """Save validation results to Excel file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = original_file_path.replace('.xlsx', f'_validated_{timestamp}.xlsx').replace('.xls', f'_validated_{timestamp}.xlsx')
        
        result_df.to_excel(output_file, index=False, engine='openpyxl')
        print(f"💾 Results saved to: {output_file}")
        return output_file


def main():
    """Main function to run the AI validation agent."""
    if len(sys.argv) != 2:
        print("Usage: python data_validation_agent.py <excel_file_path>")
        print("Example: python data_validation_agent.py data/FundHoldings_WithErrors.xlsx")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"❌ Error: File not found: {file_path}")
        sys.exit(1)
    
    try:
        # Initialize AI agent
        agent = DataValidationAIAgent()
        
        # Validate file
        result_df = agent.validate_file(file_path)
        
        if not result_df.empty:
            # Save results
            output_file = agent.save_results(result_df, file_path)
            
            # Display summary
            total_rows = len(result_df)
            correct_rows = result_df['Is_correct'].sum()
            incorrect_rows = total_rows - correct_rows
            
            print(f"\n🎯 Final Results:")
            print(f"   ✅ Correct rows: {correct_rows}/{total_rows}")
            print(f"   ❌ Incorrect rows: {incorrect_rows}/{total_rows}")
            
            if incorrect_rows > 0:
                print(f"\n❌ Rows with errors:")
                error_rows = result_df[result_df['Is_correct'] == False]
                for idx, row in error_rows.head(5).iterrows():  # Show first 5 errors
                    print(f"   Row {idx + 1}: {row['Why']}")
                if len(error_rows) > 5:
                    print(f"   ... and {len(error_rows) - 5} more errors")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
